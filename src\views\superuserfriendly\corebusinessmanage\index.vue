<template>
  <div class="coreBusiness-container">
    <Screening :options="screeningOptions" @output-screen-msg="screenMessage">
      <!-- 单位 -->
      <template #orgId="{ option }">
        <ChooseOrgV2
          v-model="option.modelValue"
          v-model:org-name="state.orgName"
          @upload-fn="orgChange"
        />
      </template>
      <!-- 专业 -->
      <template #major="{ option }">
        <el-select
          v-model="option.modelValue"
          clearable
          class="fix_width_select"
          fit-input-width
          :placeholder="$t('请选择')"
          @change="majorChange"
        >
          <el-option
            v-for="item in state.majorList"
            :key="item.id"
            :label="item.categoryName"
            :value="item.id"
          />
        </el-select>
      </template>
      <!-- 学习地图 -->
      <template #mapId="{ option }">
        <el-select
          v-model="option.modelValue"
          clearable
          class="fix_width_select"
          fit-input-width
          :placeholder="$t('请选择')"
        >
          <el-option
            v-for="item in state.maplist"
            :key="item.id"
            :label="item.mapName"
            :value="item.id"
          />
        </el-select>
      </template>
    </Screening>
    <div class="coreBusiness-main">
      <vab-button :left-span="18" :right-span="6">
        <ComboButtonGroup :btn-group="tableOptions.btnGroup" />
        <!-- 右侧导出按钮: 不使用文字 -->
        <template #right>
          <exportButton
            ref="exportBtnRef"
            :disabled="state.total"
            name-empty
            @click="
              () => {
                exportBtnRef.findRoute({
                  key: 'exportCore',
                  params: fetchData['prevQueryForm'],
                })
              }
            "
          />
        </template>
      </vab-button>
      <el-table
        v-loading="state.listLoading"
        :border="true"
        :data="state.tablelist"
        tooltip-effect="light"
        style="width: 100%"
        @selection-change="selectRows"
      >
        <el-table-column
          align="center"
          class-name="fixed_checkbox_focus_range"
          fixed="left"
          type="selection"
          width="40px"
        />
        <el-table-column
          v-for="(item, index) in tableOptions.tableColumns"
          :key="index"
          :align="item.align ? item.align : 'left'"
          :fixed="item.fixed"
          :label="$t(item.label)"
          :min-width="item.minwidth"
          :prop="item.prop"
          show-overflow-tooltip
          :width="item.width"
          :formatter="
            (row) => {
              return !row[item.prop] && row[item.prop] != 0
                ? '--'
                : row[item.prop]
            }
          "
        >
          <template #default="{ row }">
            <template v-if="item.prop == 'business'">
              <el-table-column
                align="left"
                :label="$t('一级业务')"
                prop="firstBusiness"
                show-overflow-tooltip
                :width="tableTelmpWidth.defult"
              />
              <el-table-column
                align="left"
                :label="$t('二级分类')"
                prop="secondBusiness"
                show-overflow-tooltip
                :width="tableTelmpWidth.defult"
              />
              <el-table-column
                align="left"
                :label="$t('三级业务')"
                prop="thirdBusiness"
                show-overflow-tooltip
                :width="tableTelmpWidth.defult"
              />
              <el-table-column
                align="left"
                :label="$t('业务分类')"
                prop="businessType"
                show-overflow-tooltip
                :width="tableTelmpWidth.defult"
              />
            </template>
            <template v-if="item.prop == 'post'">
              <el-table-column
                align="left"
                :label="$t('课堂培训/操作训练')"
                prop="classTrain"
                show-overflow-tooltip
                :width="tableTelmpWidth.categoryName"
              />
              <el-table-column
                align="left"
                :label="$t('是否纳入公司级培训评价')"
                prop="isCompany"
                show-overflow-tooltip
                :width="tableTelmpWidth.desc"
                :formatter="
                  (row) => {
                    return !row['isCompany'] && row['isCompany'] != 0
                      ? '--'
                      : row['isCompany'] == 1
                        ? $t('是')
                        : $t('否')
                  }
                "
              />

              <el-table-column
                align="left"
                :label="$t('在岗练习')"
                prop="guardExercise"
                show-overflow-tooltip
                :width="tableTelmpWidth.defult"
              />
              <el-table-column
                align="left"
                :label="$t('教练他人')"
                prop="education"
                show-overflow-tooltip
                :width="tableTelmpWidth.defult"
              />
            </template>
            <template v-if="item.prop == 'isSafe'">
              {{ row.isSafe === 1 ? $t('是') : $t('否') }}
            </template>
            <template v-if="item.prop == 'isReview'">
              {{ row.isReview === 1 ? $t('是') : $t('否') }}
            </template>
            <template v-if="item.prop == 'reviewYear'">
              {{
                getReviewYear(row['reviewYear'])
                  ? getReviewYear(row['reviewYear'])
                  : '--'
              }}
            </template>
            <template v-if="item.prop == 'addDate'">
              {{ handleTime(row.addDate) }}
            </template>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          fixed="right"
          :label="$t('操作')"
          width="110px"
        >
          <template #default="{ row }">
            <el-button link type="primary" @click="editCoreBusiness(row)">
              {{ $t('编辑') }}
            </el-button>
          </template>
        </el-table-column>
        <template #empty>
          <el-empty class="vab-data-empty" :description="$t('暂无数据')" />
        </template>
      </el-table>
      <!-- 分页数据 -->
      <MyPageination
        v-model:page-no="inputForm.pageNo"
        v-model:page-size="inputForm.pageSize"
        :total="state.total"
        @page-change="fetchData"
      />
    </div>
    <AddorEditCore ref="AddorEditCoreRef" @fetch-data="fetchData" />
    <exportButton v-show="false" ref="exportCompanyCoreRef" name-empty />
    <!-- 导入核心能力 -->
    <ImportCore ref="importCoreRef" @import-success="queryData" />
    <!-- 编辑核心能力 -->
    <EditCore ref="EditCoreRef" @fetch-data="queryData" />
  </div>
</template>

<script lang="ts">
  export default defineComponent({
    name: 'CoreBusinessManage',
  })
</script>
<script lang="ts" setup>
  import { $t } from '@/i18n'
  import { tableTelmpWidth } from '@/_commonTemplates/constants/tableTelmp'
  import Screening from '@/_commonTemplates/screening/Screening.vue'
  import { COMPONENT } from '@/_commonTemplates/constants/component'
  import AddorEditCore from './components/addorEditCore.vue'
  import {
    list_CompetenceRest,
    remove_CompetenceRest,
  } from '@/api/competenceapi/CompetenceRest'
  import ScreeningConfig from '~/src/_commonTemplates/screening/screeningConfig'
  import { companyProfession } from '@/api/userapi/CategorysRest'
  import { companyMaps_LearningMapRest } from '~/src/api/learnmapapi/LearningMapRest'
  import { tree_OrgRest } from '~/src/api/userapi/OrgRest'
  import { OrgTreeDTO } from '~/src/api/userapi/typings'
  import { usePageStore } from '~/src/store/modules/page'
  import ImportCore from './components/importCore.vue'
  import EditCore from './components/editCore.vue'
  import { reviewYearList } from './components/corebusinessmanage'
  import { handleTime } from '@/utils'

  const $baseMessage: any = inject('$baseMessage')
  const $baseConfirm: any = inject('$baseConfirm')

  const store = usePageStore()
  const { pageSize } = storeToRefs(store)

  const AddorEditCoreRef = ref()
  const exportBtnRef = ref()
  const exportCompanyCoreRef = ref()
  const importCoreRef = ref()
  const EditCoreRef = ref()

  const inputForm = ref<{
    orgId: any
    learnMapId: any
    professionalId: any
    name: any
    pageNo: any
    pageSize: any
  }>({
    orgId: '', // 单位
    learnMapId: '', // 学习地图
    professionalId: '', // 专业
    name: '', // 核心能力
    pageNo: 1,
    pageSize: pageSize.value,
  })

  const state = reactive({
    majorList: [],
    maplist: [],
    orgName: '',
    tablelist: [],
    total: 0,
    listLoading: false,
    selectRows: [],
  })

  const screenMessage = async (data: object) => {
    Object.keys(data).map((key) => {
      inputForm.value[key] = data[key]
    })
    await queryData()
  }
  const screeningOptions = ref<ScreeningConfig>()
  screeningOptions.value = {
    moreLimitNum: 5,
    optionList: [
      {
        type: COMPONENT.DROPDOWN_FIXED_SLOT,
        title: $t('单位：'),
        modelName: 'orgId', // 接口参数
        modelValue: '',
        slot: 'orgId',
      },
      {
        type: COMPONENT.DROPDOWN_FIXED_SLOT,
        modelName: 'major', // 接口参数
        modelValue: '',
        title: $t('专业：'),
        slot: 'major',
        isUse: true,
      },
      {
        type: COMPONENT.DROPDOWN_FIXED_SLOT,
        title: $t('学习地图：'),
        modelName: 'mapId', // 接口参数
        modelValue: '',
        slot: 'mapId',
      },
      {
        type: COMPONENT.INPUT_TEXT,
        title: $t('核心能力：'),
        modelName: 'keyword', // 接口参数
        modelValue: '',
      },
    ],
  }
  const tableOptions = ref()
  tableOptions.value = {
    btnGroup: [
      [
        {
          name: $t('添加'),
          fc: () => addFc(),
          type: 'primary',
        },
      ],
      [
        {
          name: $t('删除'),
          fc: () => delFc(),
        },
      ],
      [
        {
          name: $t('导入'),
          fc: () => importFc(),
        },
      ],
      [
        {
          name: $t('导出公司核心能力'),
          fc: () => {
            exportCompanyCoreRef.value.findRoute({
              key: 'exportCompanyCore',
            })
          },
        },
      ],
    ],
    tableColumns: [
      {
        label: '核心能力',
        prop: 'name',
        minwidth: tableTelmpWidth.defult,
        fixed: 'left',
      },
      {
        label: '业务',
        prop: 'business',
        align: 'center',
      },
      {
        label: '培训方式对应岗位',
        prop: 'post',
        align: 'center',
      },
      {
        label: '评价方式',
        prop: 'evaluateManner',
        width: tableTelmpWidth.desc,
      },
      {
        label: '是否复训',
        prop: 'isReview',
        width: tableTelmpWidth.status,
      },
      {
        label: '复制周期',
        prop: 'reviewYear',
        width: tableTelmpWidth.no,
      },
      {
        label: '是否为保命技能',
        prop: 'isSafe',
        width: tableTelmpWidth.desc,
      },
      {
        label: '创建人',
        prop: 'addFullName',
        width: tableTelmpWidth.person,
      },
      {
        label: '创建时间',
        prop: 'addDate',
        width: tableTelmpWidth.time,
      },
    ],
  }
  //添加
  const addFc = () => {
    AddorEditCoreRef.value.show()
  }

  //导入
  const importFc = () => {
    //导入和导出后面后端方法完成后再进行联调
    importCoreRef.value.show()
  }
  //编辑
  const editCoreBusiness = (row) => {
    EditCoreRef.value.show(row.linkedId)
  }
  /**
   * 获取专业数据
   * @param id 组织id
   */
  const getMajorData = async (id?) => {
    if (!id) return
    await companyProfession({ companyId: id, professionId: null })
      .then((res) => {
        const { data, code } = res
        if (code == 0) {
          state.majorList = data
        }
      })
      .catch(() => {})
  }
  /**
   * 获取地图数据
   * @param val 专业id
   */
  const getMapList = async (val) => {
    if (!val) return
    const item = screeningOptions.value.optionList.find(
      (i) => i.modelName == 'orgId'
    )
    await companyMaps_LearningMapRest({
      orgId: item.modelValue,
      professionId: val,
    })
      .then((res) => {
        const { data, code } = res
        if (code == 0) {
          state.maplist = data
        }
      })
      .catch(() => {})
  }
  /**
   * 组织内容变动
   */
  const orgChange = () => {
    const item = screeningOptions.value.optionList.find(
      (i) => i.modelName == 'orgId'
    )
    if (item) {
      //清空数据操作
      state.majorList = []
      state.maplist = []
      setVal('major', '')
      setVal('mapId', '')
      getMajorData(item.modelValue)
    }
  }
  // 专业选择
  const majorChange = (val) => {
    // 清空操作
    state.maplist = []
    setVal('mapId', '')
    if (!val) return
    getMapList(val)
  }
  /**
   * 获取组织数据
   */
  const getOrgTree = async () => {
    await tree_OrgRest({
      userOrgType: 'UserManageArea',
      parentId: '',
      level: 1,
    }).then((res) => {
      if (res?.data && Array.isArray(res?.data)) {
        const node: OrgTreeDTO = res?.data[0] || null
        if (node) {
          // 取第一个组织下的key
          const item = screeningOptions.value.optionList.find(
            (i) => i.modelName == 'orgId'
          )
          item.modelValue = node.key
          state.orgName = node.title
          // 请求专业
          getMajorData(node.key)
          // 请求初始数据
          screenMessage({
            orgId: node.key,
          })
        }
      }
    })
  }
  /**
   * 给screeningOptions.value.optionList 的某个对象赋值
   * @param id 类型
   * @param val 值
   */
  const setVal = (id, val) => {
    const item = screeningOptions.value.optionList.find(
      (i) => i.modelName == id
    )
    if (item) {
      item.modelValue = val
    }
  }
  const selectRows = (val: []) => {
    state.selectRows = val
  }

  // 选择操作的表格项
  const isChooseFc = () => {
    if (state.selectRows.length === 0) {
      $baseMessage(
        $t('未选中任何内容，请勾选后重试'),
        'error',
        'vab-hey-message-error'
      )
      return false
    }
    return true
  }

  //删除
  const delFc = () => {
    if (!isChooseFc()) return
    const ids = state.selectRows.map((item) => item.linkedId)
    const resourceIds = state.selectRows.map((item) => item.resourceId)
    $baseConfirm($t('是否确认删除？'), null, async () => {
      const { message, code } = await remove_CompetenceRest({
        orgId: inputForm.value.orgId,
        resourceIdList: resourceIds,
        linkedIdList: ids,
      })
      $baseMessage(
        message,
        `${code === 0 ? 'success' : 'error'}`,
        `vab-hey-message-${code === 0 ? 'success' : 'error'}`
      )
      fetchData()
    })
  }

  /**
   * 数据请求
   */
  const fetchData = async () => {
    state.listLoading = true
    await list_CompetenceRest(inputForm.value)
      .then((res) => {
        const { code, data } = res
        if (code == 0) {
          state.tablelist = data.list
          state.total = data.total
          // 记录当前查询条件，导出时以查询出来数据的那个条件为准
          fetchData['prevQueryForm'] = { ...inputForm.value }
        }
      })
      .finally(() => {
        state.listLoading = false
      })
  }
  const queryData = async () => {
    inputForm.value.pageNo = 1
    // 数据请求
    await fetchData()
  }
  const getReviewYear = (reviewYear: number) => {
    if (reviewYear) {
      return reviewYearList.find((i) => i.id == reviewYear)?.title
    }
  }
  onActivated(() => {
    getOrgTree()
  })
</script>

<style lang="scss" scoped>
  .coreBusiness-main {
    padding: 16px;
  }
</style>
