<template>
  <div class="coreEle-container">
    <el-dialog
      v-model="dialogVisible"
      destroy-on-close
      close-on-click-modal
      close-on-press-escape
      :title="dialogFlag == 'ele' ? $t('添加能力要素') : $t('添加核心能力')"
      width="1080"
      :before-close="close"
    >
      <div class="coreEle-main">
        <div class="coreEle-left">
          <div class="coreEle-left-top">
            <el-form
              ref="form"
              :model="inputForm"
              :inline="false"
              class="coreEle_form"
            >
              <!-- 单位 -->
              <el-form-item>
                <ChooseOrgV2
                  v-model="inputForm.orgId"
                  v-model:org-name="state.orgName"
                  :placeholder="$t('请选择单位')"
                  @upload-fn="orgChange"
                />
              </el-form-item>
              <!-- 专业需求列 -->
              <el-form-item>
                <el-select
                  v-model="inputForm.sequence"
                  clearable
                  fit-input-width
                  :placeholder="$t('请选择专业序列')"
                  @change="sequenceChange"
                >
                  <el-option
                    v-for="item in state.sequenceList"
                    :key="item.id"
                    :label="item.categoryName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <!-- 专业 -->
              <el-form-item>
                <el-select
                  v-model="inputForm.major"
                  clearable
                  fit-input-width
                  :placeholder="$t('请选择专业')"
                  @change="majorChange"
                >
                  <el-option
                    v-for="item in state.majorList"
                    :key="item.id"
                    :label="item.categoryName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <!-- 学习地图 -->
              <el-form-item>
                <el-select
                  v-model="inputForm.mapId"
                  clearable
                  fit-input-width
                  :placeholder="$t('请选择公司地图')"
                >
                  <el-option
                    v-for="item in state.maplist"
                    :key="item.id"
                    :label="item.mapName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <!-- 核心能力 -->
              <el-form-item>
                <el-input
                  v-model="inputForm.keyword"
                  :maxlength="80"
                  fit-input-width
                  clearable
                  :placeholder="$t('请输入核心能力名称')"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  class="form_onSubmit"
                  type="primary"
                  :icon="Search"
                  @click="searchSub"
                />
              </el-form-item>
            </el-form>
          </div>
          <div class="coreEle-left-table">
            <el-table
              ref="tableRef"
              v-loading="state.listLoading"
              :border="true"
              :data="state.tableList"
              tooltip-effect="light"
              style="width: 100%"
              @selection-change="selectRowsFc"
            >
              <el-table-column
                align="center"
                class-name="fixed_checkbox_focus_range"
                fixed="left"
                type="selection"
                width="40px"
              />
              <el-table-column
                align="left"
                fixed="left"
                prop="name"
                show-overflow-tooltip
                :min-width="tableTelmpWidth.name"
                :label="$t('核心能力名称')"
              />
              <el-table-column
                :label="$t('一级业务')"
                align="left"
                prop="firstBusiness"
                show-overflow-tooltip
                :width="tableTelmpWidth.defult"
              />
              <el-table-column
                :label="$t('二级业务')"
                align="left"
                :width="tableTelmpWidth.defult"
                prop="secondBusiness"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('三级业务')"
                align="left"
                :width="tableTelmpWidth.defult"
                prop="thirdBusiness"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('业务分类')"
                align="left"
                :width="tableTelmpWidth.defult"
                prop="businessType"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('是否为保命技能')"
                :width="tableTelmpWidth.no"
                prop="isSafe"
                fixed="right"
                show-overflow-tooltip
                :formatter="
                  (row) =>
                    !row.isSafe && row.isSafe !== 0
                      ? '--'
                      : row.isSafe === 1
                        ? $t('是')
                        : $t('否')
                "
              />
              <template #empty>
                <el-empty
                  class="vab-data-empty"
                  :description="$t('暂无数据')"
                />
              </template>
            </el-table>
            <!-- 分页栏 -->
            <MyPageination
              v-model:page-no="inputForm.pageNo"
              v-model:page-size="inputForm.pageSize"
              :total="state.total"
              @page-change="fetchData"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="close">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="onSave">{{ $t('确定') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { Search } from '@element-plus/icons-vue'
  import { $t } from '@/i18n'
  import { getRootProfession_queryPage } from '@/api/userapi/CategorysRest'
  import { companyProfession } from '@/api/userapi/CategorysRest'
  import { companyMaps_LearningMapRest } from '~/src/api/learnmapapi/LearningMapRest'
  import { list_CompetenceRest } from '~/src/api/competenceapi/CompetenceRest'
  import { usePageStore } from '@/store/modules/page'
  import { tableTelmpWidth } from '~/src/_commonTemplates/constants/tableTelmp'
  import { create_ProjectElementRest } from '~/src/api/projectapi/projectElementRest'

  const $baseMessage: any = inject('$baseMessage')
  const $baseLoading: any = inject('$baseLoading')

  const store = usePageStore()
  const { pageSize } = storeToRefs(store)
  const dialogVisible = ref<boolean>(false)
  const dialogFlag = ref<string>()
  const selectRows = ref<any[]>([])
  const tableRef = ref()
  const route = useRoute()

  const emit = defineEmits(['fetch-data'])

  const state = reactive<{
    orgName: string
    majorList: any //专业列表接口
    maplist: any //地图列表接口
    sequenceList: any //专业序列下拉列表
    tableList: any
    listLoading: boolean
    total: number
    selectTabRow: any
  }>({
    orgName: '',
    majorList: [],
    maplist: [],
    sequenceList: [],
    total: 0,
    listLoading: false,
    tableList: [],
    selectTabRow: [],
  })

  const inputForm = ref({
    orgId: '', //单位
    major: '', //专业
    sequence: '', //专业序列
    mapId: '', //公司地图
    keyword: '', //要素名称
    pageNo: 1,
    pageSize: pageSize.value,
  })

  const close = () => {
    dialogVisible.value = false

    nextTick(() => {
      inputForm.value = {
        orgId: '', //单位
        major: '', //专业
        sequence: '', //专业序列
        mapId: '', //公司地图
        keyword: '', //要素名称
        pageNo: 1,
        pageSize: 10,
      }
      state.majorList = []
      state.maplist = []
      state.sequenceList = []
      state.tableList = []
      state.selectTabRow = []
      selectRows.value = []
    })
  }

  const selectRowsFc = (val: []) => {
    selectRows.value = val
    // 每次当前分页触发选中 则储存当前触发的数据
    selectRows.value.map((item) => {
      state.selectTabRow.push(item)
    })
  }

  const delCore = (item) => {
    if (!item) return
    const index = selectRows.value.findIndex(
      (i) => item.linkedId === i.linkedId
    )
    // 如果大于-1 则当前分页显示，则需要取消选中
    if (index > -1) {
      tableRef.value.toggleRowSelection(item, false)
    }
    // 储存是否选中的list 中删除
    state.selectTabRow.splice(index, 1)
  }

  const clearAll = () => {
    // 表格取消选中
    selectRows.value.map((item) => {
      tableRef.value.toggleRowSelection(item, false)
    })
    //取消选中后再执行清空
    selectRows.value = []
    state.selectTabRow = []
  }

  const onSave = async () => {
    if (state.selectTabRow.length <= 0) {
      $baseMessage($t('请选择核心能力'), 'error', 'vab-hey-message-error')
      return
    }
    const loading = $baseLoading()
    const ids = selectRows.value.map((item: any) => item.linkedId).join(',')
    await create_ProjectElementRest({
      taskId: route.query.id,
      ids: ids,
      type: 1,
    })
      .then((res) => {
        if (res.code == 0) {
          emit('fetch-data', state.selectTabRow)
          close()
        }
        $baseMessage(
          res.code,
          res.code == 0 ? 'success' : 'error',
          res.code == 0 ? 'vab-hey-message-success' : 'vab-hey-message-error'
        )
      })
      .finally(() => {
        loading.close()
      })
  }
  /**
   * flag = ele 能力要素  core 核心能力
   * @param flag
   */
  const show = (flag: string) => {
    if (!flag) return
    dialogFlag.value = flag
    dialogVisible.value = true
    sequenceData()
  }
  // 单位切换
  const orgChange = async () => {
    inputForm.value.major = ''
    inputForm.value.mapId = ''
    state.majorList = []
    state.maplist = []
  }
  // 获取专业序列
  const sequenceData = async () => {
    await getRootProfession_queryPage().then((res) => {
      if (res.code == 0) {
        state.sequenceList = res.data
      }
    })
  }
  // 专业序列切换
  const sequenceChange = async () => {
    await companyProfession({
      companyId: inputForm.value.orgId,
      professionId: inputForm.value.sequence,
    }).then((res) => {
      if (res.code == 0) {
        state.majorList = res.data
        inputForm.value.major = ''
        inputForm.value.mapId = ''
        state.maplist = []
      }
    })
  }
  // 专业切换
  const majorChange = async () => {
    state.maplist = []
    inputForm.value.mapId = ''
    await companyMaps_LearningMapRest({
      orgId: inputForm.value.orgId,
      professionId: inputForm.value.major,
    })
      .then((res) => {
        const { data, code } = res
        if (code == 0) {
          state.maplist = data
        }
      })
      .catch(() => {})
  }

  const searchSub = async () => {
    inputForm.value.pageNo = 1
    fetchData()
  }
  const fetchData = async () => {
    state.listLoading = true
    await list_CompetenceRest({
      orgId: inputForm.value.orgId,
      mapId: inputForm.value.mapId,
      keyword: inputForm.value.keyword,
      pageNo: inputForm.value.pageNo,
      pageSize: inputForm.value.pageSize,
    })
      .then((res) => {
        if (res.code == 0) {
          state.tableList = res.data.list
          state.total = res.data.total
        }
      })
      .finally(() => {
        state.listLoading = false
      })
  }
  defineExpose({
    // 提供给外面在active时候刷新开通应用的数据
    show,
  })
</script>

<style lang="scss" scoped>
  .coreEle-main {
    width: 100%;
    height: 540px;
    display: flex;
    align-items: stretch;
    .coreEle-left {
      width: 100%;
      padding: 0 20px 0 0;
      border-right: 1px solid #dcdfe6;
      margin-right: 20px;

      .coreEle_form {
        width: 100%;
        padding-right: 46px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        position: relative;
        :deep() {
          .el-form-item {
            margin: 0 16px 16px 0;
            width: calc((100% - 48px) / 3);

            .el-select {
              width: 100%;
            }
          }
        }
        .form_onSubmit {
          width: 46px;
          height: 32px;
        }
      }
    }
  }
</style>
