<template>
  <div class="edit-core">
    <el-drawer
      v-model="drawerVisible"
      :before-close="onBeforeClose"
      destroy-on-close
      direction="rtl"
      modal-class="ui-drawer"
      :size="DRAWER_SIZE.MEDIUM"
    >
      <template #header>
        <h3>{{ $t('编辑核心能力') }}</h3>
      </template>
      <AnChor :titles="[{ id: 'coreArrange', name: $t('核心能力设置') }]">
        <el-form
          ref="formRef"
          class="demo-form-inline form-box"
          label-position="right"
          label-width="auto"
          :model="form"
          :rules="rules"
        >
          <AnchorItem id="coreArrange">
            <!-- 课堂培训/操作训练 -->
            <el-form-item :label="$t('课堂培训/操作训练：')" prop="classTrain">
              <el-select
                v-model="form.classTrain"
                clearable
                class="fix_width_select"
                fit-input-width
                :placeholder="$t('请选择')"
              >
                <el-option
                  v-for="item in state.classTrainList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <!-- 在岗练习 -->
            <el-form-item :label="$t('在岗练习：')" prop="guardExercise">
              <el-select
                v-model="form.guardExercise"
                clearable
                class="fix_width_select"
                fit-input-width
                :placeholder="$t('请选择')"
              >
                <el-option
                  v-for="item in state.guardExerciseList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <!-- 教练他人 -->
            <el-form-item :label="$t('教练他人：')" prop="education">
              <el-select
                v-model="form.education"
                clearable
                class="fix_width_select"
                fit-input-width
                :placeholder="$t('请选择')"
              >
                <el-option
                  v-for="item in state.educationList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <!-- 是否复训 -->
            <el-form-item :label="$t('是否复训：')" prop="isReview">
              <el-radio-group v-model="form.isReview" @change="radioChange">
                <el-radio :label="0">{{ $t('否') }}</el-radio>
                <el-radio :label="1">{{ $t('是') }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 复训周期 -->
            <el-form-item
              v-if="form.isReview === 1"
              :label="$t('复训周期：')"
              prop="reviewYear"
            >
              <el-select
                v-model="form.reviewYear"
                clearable
                class="fix_width_select"
                fit-input-width
                :placeholder="$t('请选择')"
              >
                <el-option
                  v-for="item in state.reviewYearList"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <!-- 能力要素 -->
            <el-form-item :label="$t('能力要素：')" prop="elementName">
              <el-input
                v-model="form.elementName"
                clearable
                :maxlength="1200"
                :placeholder="$t('请输入能力要素')"
                show-word-limit
                type="textarea"
                :rows="6"
              />
            </el-form-item>
          </AnchorItem>
        </el-form>
      </AnChor>

      <template #footer>
        <div style="flex: auto">
          <el-button @click="onCancel">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="onSave">{{ $t('保存') }}</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { $t } from '@/i18n'
  import { FormRules } from 'element-plus'
  import { beforeClose } from '@/utils'
  import {
    list_CompetenceRest_1,
    editOrgCoreCompetence_CompetenceRest,
    getLimitPostList_CompetenceRest,
  } from '@/api/competenceapi/CompetenceRest'
  import { reviewYearList } from './corebusinessmanage'

  const $baseLoading: any = inject('$baseLoading')
  const $baseMessage: any = inject('$baseMessage')

  const emit = defineEmits(['fetch-data'])

  // 抽屉显示状态
  const drawerVisible = ref<boolean>(false)

  // 表单引用
  const formRef = ref()

  // 存储原始数据用于比较
  const oldValue = ref<string>('')

  const state = reactive<{
    reviewYearList: any[]
    classTrainList: any[]
    guardExerciseList: any[]
    educationList: any[]
  }>({
    reviewYearList: reviewYearList,
    classTrainList: [],
    guardExerciseList: [],
    educationList: [],
  })

  // 表单数据
  const form = ref<{
    id: string // 关联记录id
    orgId: string // 单位id
    resourceId: string // 资源id
    classTrain: string // 操作类型 课堂培训/操作练习
    guardExercise: string // 操作类型 在岗练习
    education: string // 操作类型 教练他人
    professionId: string // 专业id
    isReview: number // 是否复训 0-否 1-是
    parentIsReview: number // 能力资源是否复训状态 0-否 1-是
    reviewYear: number // 复训周期
    elementName: string // 要素名称
    isLocal: number // 是否为本地核心能力 0-否 1-是
  }>({
    id: '', // 关联记录id
    orgId: '', // 单位id
    resourceId: '', // 资源id
    classTrain: '', // 课堂培训/操作训练
    guardExercise: '', // 在岗练习
    education: '', // 教练他人
    professionId: '', // 专业id
    isReview: 0, // 是否复训 0-否 1-是
    parentIsReview: 0, // 能力资源是否复训状态 0-否 1-是
    reviewYear: null, // 复训周期
    elementName: '', // 要素名称
    isLocal: 0, // 是否为本地核心能力 0-否 1-是
  })

  // 表单验证规则
  const rules = ref<FormRules>({
    classTrain: [
      {
        required: true,
        validator: (_rule: any, value: any, callback: any) => {
          if (!form.value.classTrain) {
            return callback($t('请选择课堂培训/操作训练'))
          }
          callback()
        },
        trigger: 'blur',
      },
    ],
    guardExercise: [
      {
        required: true,
        validator: (_rule: any, value: any, callback: any) => {
          if (!form.value.guardExercise) {
            return callback($t('请选择在岗练习'))
          }
          callback()
        },
        trigger: 'blur',
      },
    ],
    education: [
      {
        required: true,
        validator: (_rule: any, value: any, callback: any) => {
          if (!form.value.education) {
            return callback($t('请选择教练他人'))
          }
          callback()
        },
        trigger: 'blur',
      },
    ],
    isReview: [
      { required: true, message: $t('请选择是否复训'), trigger: 'change' },
    ],
    reviewYear: [
      {
        required: true,
        validator: (_rule: any, value: any, callback: any) => {
          if (form.value.isReview === 1) {
            if (!form.value.reviewYear) {
              return callback($t('请选择复训周期'))
            }
          }
          callback()
        },
        trigger: 'blur',
      },
    ],
    elementName: [
      { required: true, message: $t('请输入能力要素'), trigger: 'blur' },
      { max: 1200, message: $t('最多可输入100个字符'), trigger: 'blur' },
    ],
  })

  /**
   * 获取核心能力详情
   */
  const fetchCoreDetail = async (recordId: string) => {
    const loading = $baseLoading()
    try {
      const { data, code } = await list_CompetenceRest_1({ recordId })
      if (code === 0) {
        // 将接口返回的数据赋值给表单
        if (data) {
          Object.keys(form.value).forEach((key) => {
            if (key !== 'reviewYear') {
              form.value[key] = data[key]
            } else {
              form.value[key] = data[key] ? data[key] : null
            }
          })
        }
        const arr = [1, 3, 4]
        arr.forEach((item) => {
          getSelectList(item, data.resourceId, data.professionId)
        })
        // 保存原始数据
        oldValue.value = JSON.stringify(form.value)
      }
    } finally {
      loading.close()
    }
  }

  /**
   * 保存核心能力
   */
  const onSave = async () => {
    if (!formRef.value) return

    formRef.value.validate(async (valid: boolean) => {
      if (valid) {
        const loading = $baseLoading()
        try {
          form.value.reviewYear = form.value.reviewYear ?? 0
          const { code, message } = await editOrgCoreCompetence_CompetenceRest(
            form.value
          )
          if (code === 0) {
            $baseMessage(message, 'success', 'vab-hey-message-success')
            drawerVisible.value = false
            emit('fetch-data')
            resetForm()
          } else {
            $baseMessage(message, 'error', 'vab-hey-message-error')
          }
        } catch (error) {
          $baseMessage($t('保存失败'), 'error', 'vab-hey-message-error')
        } finally {
          loading.close()
        }
      }
    })
  }

  /**
   * 取消操作
   */
  const onCancel = () => {
    onBeforeClose()
  }

  /**
   * 关闭前的校验
   */
  const onBeforeClose = () => {
    const currentValue = JSON.stringify(form.value)
    if (oldValue.value !== currentValue) {
      beforeClose(closeDrawer)
    } else {
      closeDrawer()
    }
  }

  /**
   * 关闭抽屉
   */
  const closeDrawer = () => {
    drawerVisible.value = false
    resetForm()
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    form.value = {
      id: '', // 关联记录id
      orgId: '', // 单位id
      resourceId: '', // 资源id
      classTrain: '', // 课堂培训/操作训练
      guardExercise: '', // 在岗练习
      education: '', // 教练他人
      professionId: '', // 专业id
      isReview: 0, // 是否复训 0-否 1-是
      parentIsReview: 0, // 能力资源是否复训状态 0-否 1-是
      reviewYear: null, // 复训周期
      elementName: '', // 要素名称
      isLocal: 0, // 是否为本地核心能力 0-否 1-是
    }
    oldValue.value = ''
    formRef.value?.resetFields()
  }

  /**
   * 显示抽屉
   */
  const show = async (linkedId) => {
    if (!linkedId) return
    drawerVisible.value = true
    await fetchCoreDetail(linkedId)
  }

  /**
   * 操作方式 1-课堂培训 3- 在岗练习 4-教练他人
   * @param categoryType
   */
  const getSelectList = async (
    categoryType: any,
    resourceId: string,
    professionId: string
  ) => {
    if (!categoryType) return
    const { data, code } = await getLimitPostList_CompetenceRest({
      resourceId: resourceId,
      professionId: professionId,
      categoryType: categoryType,
    })
    if (code === 0) {
      if (categoryType == 1) {
        state.classTrainList = data
      } else if (categoryType == 3) {
        state.guardExerciseList = data
      } else if (categoryType == 4) {
        state.educationList = data
      }
    }
  }

  const radioChange = () => {
    if (!form.value.isReview) {
      form.value.reviewYear = null
    }
  }

  // 暴露方法给父组件
  defineExpose({
    show,
  })
</script>

<style lang="scss" scoped>
  :deep() {
    .main-container {
      height: 100%;
    }
    .slot_container {
      height: 100%;
    }
  }
</style>
