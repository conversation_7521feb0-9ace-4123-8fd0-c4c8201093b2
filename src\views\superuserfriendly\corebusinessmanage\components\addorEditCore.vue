<template>
  <div class="add-information">
    <el-drawer
      v-model="inforDrawer"
      :before-close="onBack"
      destroy-on-close
      direction="rtl"
      modal-class="ui-drawer"
      :size="DRAWER_SIZE.MEDIUM"
    >
      <template #header>
        <h3>{{ title }}</h3>
      </template>
      <AnChor :titles="[{ id: 'coreArrange', name: $t('核心能力设置') }]">
        <el-form
          ref="formRef"
          class="demo-form-inline form-box"
          label-position="right"
          label-width="auto"
          :model="inputForm"
          :rules="rules"
        >
          <div class="information_content">
            <AnchorItem id="coreArrange">
              <el-form-item :label="$t('单位：')" prop="orgId">
                <ChooseOrgV2
                  v-model="inputForm.orgId"
                  v-model:org-name="inputForm.orgName"
                  text
                  @upload-fn="orgChange"
                />
              </el-form-item>
              <el-form-item :label="$t('专业：')" prop="professionId">
                <el-select
                  v-model="inputForm.professionId"
                  clearable
                  fit-input-width
                  :placeholder="$t('请选择')"
                  class="fix_width_select"
                  @change="majorChange"
                >
                  <el-option
                    v-for="item in professionSelectList"
                    :key="item.id"
                    :label="item.categoryName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('学习地图：')" prop="learnMapId">
                <el-select
                  v-model="inputForm.learnMapId"
                  clearable
                  fit-input-width
                  :placeholder="$t('请选择')"
                  class="fix_width_select"
                >
                  <el-option
                    v-for="item in mapSelectList"
                    :key="item.id"
                    :label="item.mapName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('班组：')" prop="teamName">
                <el-input
                  v-model="inputForm.teamName"
                  class="fix_width_input"
                  clearable
                  maxlength="100"
                  :placeholder="$t('请输入')"
                  show-word-limit
                />
                <span class="teamName_tips">
                  {{
                    $t(
                      '单位引用核心能力名称规则：公司发布的核心能力清单名+单位名称+ 班组'
                    )
                  }}
                </span>
              </el-form-item>
              <el-form-item :label="$t('引用核心能力：')" prop="teamName">
                <el-checkbox-group v-model="inputForm.resourceIdList">
                  <el-checkbox
                    v-for="item in mapSelectList"
                    :key="item.id"
                    :label="item.id"
                  >
                    {{ item.mapName }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </AnchorItem>
          </div>
        </el-form>
      </AnChor>
      <template #footer>
        <el-button @click="onBack()">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="onSave()">{{ $t('确定') }}</el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { $t } from '@/i18n'
  import { saveOrgCoreCompetence_CompetenceRest } from '@/api/competenceapi/CompetenceRest'
  import { beforeClose } from '@/utils'
  import { companyMaps_LearningMapRest } from '@/api/learnmapapi/LearningMapRest'
  import { companyProfession } from '@/api/userapi/CategorysRest'

  const inforDrawer = ref<boolean>(false) // 添加编辑资讯弹窗
  const title = ref<string>($t('添加核心能力'))
  const historyItem = ref<any>()

  const $baseMessage: any = inject('$baseMessage')

  const emit = defineEmits(['fetch-data'])
  //表单对象
  const inputForm = ref<{
    orgId: string // 单位id
    orgName: string // 单位名称
    learnMapId: string // 地图id
    professionId: string // 专业id
    teamName: string // 班组id
    resourceIdList: any[] // 资源记录id
  }>({
    orgId: '', // 单位id
    orgName: '', // 单位名称
    learnMapId: '', // 地图id
    professionId: '', // 专业id
    teamName: '', // 班组id
    resourceIdList: [], // 资源记录id
  })
  //state
  const state = reactive<{
    formRef: any
    rules: any
    professionSelectList: any
    mapSelectList: any
  }>({
    formRef: ref(),
    rules: {
      teamName: [
        {
          required: true,
          message: $t('请输入班组'),
          trigger: 'change',
        },
      ],
      orgId: [
        {
          required: true,
          trigger: 'blur',
          validator: (rule: any, value: any, callback: any) => {
            if (inputForm.value.orgId) {
              return callback()
            }
            return callback(new Error($t('请选择单位')))
          },
        },
      ],
      professionId: [
        {
          required: true,
          trigger: 'blur',
          validator: (rule: any, value: any, callback: any) => {
            if (inputForm.value.professionId) {
              return callback()
            }
            return callback(new Error($t('请选择专业')))
          },
        },
      ],
      learnMapId: [
        {
          required: true,
          trigger: 'blur',
          validator: (rule: any, value: any, callback: any) => {
            if (inputForm.value.learnMapId) {
              return callback()
            }
            return callback(new Error($t('请选择学习地图')))
          },
        },
      ],
    },
    //专业下拉数据
    professionSelectList: [],
    //学习地图下拉数据
    mapSelectList: [],
  })
  const { formRef, rules, professionSelectList, mapSelectList } = toRefs(state)

  /**
   * 保存提交
   */
  const onSave = async () => {
    formRef.value.validate(async (tvalid: boolean) => {
      if (tvalid) {
        await saveOrgCoreCompetence_CompetenceRest(inputForm.value)
          .then((res) => {
            let cate = false
            if (res.code == 0) {
              cate = true
              clearDialog()
              emit('fetch-data')
            }
            $baseMessage(
              res.message,
              cate ? 'success' : 'error',
              cate ? 'vab-hey-message-success' : 'vab-hey-message-error'
            )
          })
          .catch(() => {})
      }
    })
  }

  const onBack = () => {
    if (historyItem.value != JSON.stringify(inputForm.value)) {
      beforeClose(clearDialog)
    } else {
      clearDialog()
    }
  }

  const clearDialog = () => {
    inputForm.value = {
      orgId: '', // 单位id
      orgName: '', // 单位名称
      learnMapId: '', // 地图id
      professionId: '', // 专业id
      teamName: '', // 班组id
      resourceIdList: [], // 资源记录id
    }
    formRef.value?.resetFields()
    nextTick(() => (inforDrawer.value = false))
  }

  /**
   * 获取专业数据
   * @param id 组织id
   */
  const getMajorData = async () => {
    await companyProfession({
      companyId: inputForm.value.orgId,
      professionId: null,
    })
      .then((res) => {
        const { data, code } = res
        if (code == 0) {
          state.professionSelectList = data
        }
      })
      .catch(() => {})
  }
  /**
   * 获取地图数据
   * @param val 专业id
   */
  const getMapList = async (val: string) => {
    await companyMaps_LearningMapRest({
      orgId: inputForm.value.orgId,
      professionId: val,
    })
      .then((res) => {
        const { data, code } = res
        if (code == 0) {
          state.mapSelectList = data
        }
      })
      .catch(() => {})
  }
  /**
   * 组织内容变动
   */
  const orgChange = () => {
    //清空数据操作
    state.professionSelectList = []
    state.mapSelectList = []
    inputForm.value.professionId = ''
    inputForm.value.learnMapId = ''
    // 获取专业下拉数据
    getMajorData()
  }

  // 专业选择
  const majorChange = (val: string) => {
    // 清空操作
    state.mapSelectList = []
    inputForm.value.learnMapId = ''
    getMapList(val)
  }

  const show = async (item?: any) => {
    inforDrawer.value = true
    if (item) {
      title.value = $t('编辑核心能力')
      // 编辑模式下设置表单数据
      inputForm.value.orgId = item.orgId || ''
      inputForm.value.orgName = item.orgName || ''
      inputForm.value.learnMapId = item.learnMapId || ''
      inputForm.value.professionId = item.professionId || ''
      inputForm.value.teamName = item.teamName || ''
      inputForm.value.resourceIdList = item.resourceIdList || []

      //专业列表
      getMajorData()
      //地图列表
      if (inputForm.value.professionId) {
        getMapList(inputForm.value.professionId)
      }
    }
    historyItem.value = JSON.stringify(inputForm.value)
  }
  defineExpose({
    show,
  })
</script>
<style lang="scss" scoped>
  :deep() {
    .main-container {
      height: 100%;
    }
    .slot_container {
      height: 100%;
    }
  }
  .teamName_tips {
    font-size: 14px;
    color: #999;
  }
</style>
